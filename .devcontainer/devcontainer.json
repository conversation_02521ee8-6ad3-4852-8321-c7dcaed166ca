// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.208.0/containers/javascript-node-mongo
// Update the VARIANT arg in docker-compose.redis-cluster.yml to pick a Node.js version
{
  "name": "Novu",
  "dockerComposeFile": "docker-compose.yml",
  "service": "app",
  "workspaceFolder": "/workspace",
  "hostRequirements": {
    "cpus": 4
  },
  // Add the IDs of extensions you want installed when the container is created.
  "customizations": {
    "vscode": {
      "extensions": ["dbaeumer.vscode-eslint", "mongodb.mongodb-vscode"]
    }
  },
  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [4200, 3000, 27017],

  "onCreateCommand": "npm run setup:project -- --exclude=@novu/api-service,@novu/worker,@novu/web,@novu/widget",

  // Comment out connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  "remoteUser": "node",
  "features": {
    "github-cli": "latest",
    "ghcr.io/devcontainers-contrib/features/pnpm:2": {}
  }
}
