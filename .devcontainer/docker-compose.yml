services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Update 'VARIANT' to pick an LTS version of Node.js: 16, 14, 12.
        # Append -bullseye or -buster to pin to an OS version.
        # Use -bullseye variants on local arm64/Apple Silicon.
        VARIANT: 20-bullseye
    volumes:
      - ..:/workspace:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db
    # Uncomment the next line to use a non-root user for all processes.
    # user: node

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)

  db:
    image: mongo:8.0.3
    restart: unless-stopped
    volumes:
      - mongodb-content:/data/db
    # Uncomment to change startup options
    # environment:
    #  MONGO_INITDB_ROOT_USERNAME: root
    #  MONGO_INITDB_ROOT_PASSWORD: example
    #  MONGO_INITDB_DATABASE: your-database-here

    # Add "forwardPorts": ["27017"] to **devcontainer.json** to forward MongoDB locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)

volumes:
  mongodb-content: null
