<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="API" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/apps/api/package.json" />
    <command value="run" />
    <scripts>
      <script value="start:dev" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="NOVU_SECRET_KEY" value="''" />
    </envs>
    <method v="2" />
  </configuration>
</component>