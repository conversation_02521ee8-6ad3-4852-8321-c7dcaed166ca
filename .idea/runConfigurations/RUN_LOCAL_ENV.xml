<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="RUN LOCAL ENV" type="CompoundRunConfigurationType">
    <toRun name="API" type="js.build_tools.npm" />
    <toRun name="APPLICATION GENERIC" type="js.build_tools.npm" />
    <toRun name="DAL" type="js.build_tools.npm" />
    <toRun name="DESIGN-SYSTEM" type="js.build_tools.npm" />
    <toRun name="EE-AUTH" type="js.build_tools.npm" />
    <toRun name="NOVU UI" type="js.build_tools.npm" />
    <toRun name="SHARED" type="js.build_tools.npm" />
    <toRun name="TESTING" type="js.build_tools.npm" />
    <toRun name="WEB" type="js.build_tools.npm" />
    <toRun name="WORKER" type="js.build_tools.npm" />
    <toRun name="WS" type="js.build_tools.npm" />
    <method v="2" />
  </configuration>
</component>