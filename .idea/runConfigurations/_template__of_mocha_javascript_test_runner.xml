<component name="ProjectRunConfigurationManager">
  <configuration default="true" type="mocha-javascript-test-runner">
    <node-interpreter>project</node-interpreter>
    <node-options />
    <working-directory />
    <pass-parent-env>true</pass-parent-env>
    <envs>
      <env name="E2E_RUNNER" value="true" />
      <env name="NODE_ENV" value="test" />
      <env name="NOVU_ENTERPRISE" value="true" />
      <env name="TS_NODE_COMPILER_OPTIONS" value="{&quot;strictNullChecks&quot;: false}" />
    </envs>
    <ui />
    <extra-mocha-options>--require ts-node/register --exit --file e2e/setup.ts</extra-mocha-options>
    <test-kind>DIRECTORY</test-kind>
    <test-directory />
    <recursive>false</recursive>
    <method v="2" />
  </configuration>
</component>