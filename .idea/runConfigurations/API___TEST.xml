<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="API - TEST" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/apps/api/package.json" />
    <command value="run" />
    <scripts>
      <script value="start:test" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="NOVU_ENTERPRISE" value="true" />
    </envs>
    <method v="2" />
  </configuration>
</component>
