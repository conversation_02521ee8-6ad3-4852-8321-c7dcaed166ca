<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="WORKER - TEST" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/apps/worker/package.json" />
    <command value="run" />
    <scripts>
      <script value="start:test" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="NOVU_ENTERPRISE" value="true" />
    </envs>
    <method v="2" />
  </configuration>
</component>