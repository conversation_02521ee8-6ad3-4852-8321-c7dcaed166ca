<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CommitMessageInspectionProfile">
    <profile version="1.0">
      <inspection_tool class="CommitFormat" enabled="true" level="WARNING" enabled_by_default="true" />
      <inspection_tool class="CommitNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" />
    </profile>
  </component>
  <component name="IssueNavigationConfiguration">
    <option name="links">
      <list>
        <IssueNavigationLink>
          <option name="issueRegexp" value="([A-Za-z]+)-(\d+)" />
          <option name="linkRegexp" value="https://linear.app/relayed/issue/$1-$2" />
        </IssueNavigationLink>
      </list>
    </option>
  </component>
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/.source" vcs="Git" />
  </component>
</project>
