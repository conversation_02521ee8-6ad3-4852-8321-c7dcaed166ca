name: '🐛 Bug Report'
description: 'Submit a bug report to help us improve'
title: '🐛 Bug Report: '
labels: ['type: bug']
body:
  - type: markdown
    attributes:
      value: We value your time and effort to submit this bug report. 🙏
  - type: textarea
    id: description
    validations:
      required: true
    attributes:
      label: '📜 Description'
      description: 'A clear and concise description of what the bug is.'
      placeholder: 'It bugs out when ...'
  - type: textarea
    id: steps-to-reproduce
    validations:
      required: true
    attributes:
      label: '👟 Reproduction steps'
      description: 'How do you trigger this bug? Please walk us through it step by step.'
      placeholder: "1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See the error"
  - type: textarea
    id: expected-behavior
    validations:
      required: true
    attributes:
      label: '👍 Expected behavior'
      description: 'What did you think should happen?'
      placeholder: 'It should ...'
  - type: textarea
    id: actual-behavior
    validations:
      required: true
    attributes:
      label: '👎 Actual Behavior with Screenshots'
      description: 'What did actually happen? Add screenshots, if applicable.'
      placeholder: 'It actually ...'
  - type: input
    id: novu-version
    validations:
      required: true
    attributes:
      label: Novu version
      description: In case of self-hosting or local installation mention the Novu version like 0.17.0. If using our cloud-managed solution, mention Novu SaaS.
      placeholder: Novu SaaS
  - type: input
    id: npm-version
    validations:
      required: false
    attributes:
      label: npm version
      description: In case of self-hosting or local installation mention the npm version. If using our cloud-managed solution, mention NA.
      placeholder: 7.0.0
  - type: input
    id: node-version
    validations:
      required: false
    attributes:
      label: node version
      description: In case of self-hosting or local installation mention the node version. If using our cloud-managed solution, mention NA.
      placeholder: 20.0.0
  - type: textarea
    id: additional-context
    validations:
      required: false
    attributes:
      label: '📃 Provide any additional context for the Bug.'
      description: 'Add any other context about the problem here.'
      placeholder: 'It actually ...'
  - type: checkboxes
    id: no-duplicate-issues
    attributes:
      label: '👀 Have you spent some time to check if this bug has been raised before?'
      options:
        - label: "I checked and didn't find a similar issue"
          required: true
  - type: checkboxes
    id: read-code-of-conduct
    attributes:
      label: '🏢 Have you read the Contributing Guidelines?'
      options:
        - label: 'I have read the [Contributing Guidelines](https://github.com/novuhq/novu/blob/main/CONTRIBUTING.md)'
          required: true
  - type: dropdown
    attributes:
      label: Are you willing to submit PR?
      description: This is absolutely not required, but we are happy to guide you in the contribution process. Find us in help-needed channel on [Discord](https://discord.gg/9wcGSf22PM)!
      options:
        - 'Yes I am willing to submit a PR!'
