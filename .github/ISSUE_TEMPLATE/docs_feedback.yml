name: 📚 Docs Feedback
description: Improve Novu documentation
labels: ['type: docs-feedback']
title: '📚 Docs Feedback: '
body:
  - type: markdown
    attributes:
      value: |
        Please provide a searchable summary of the issue in the title above ⬆️.

        Thanks for contributing by creating an issue! ❤️
  - type: checkboxes
    attributes:
      label: Duplicates
      description: Please [search the history](https://github.com/novuhq/novu/issues) to see if an issue already exists for the same problem.
      options:
        - label: I have searched the existing issues
          required: true

  - type: input
    id: page-url
    attributes:
      label: Related page
      description: Which page of the documentation is this about?
      placeholder: https://docs.novu.co/platform/topics
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Kind of issue
      description: What kind of problem are you facing?
      options:
        - Unclear explanations
        - Missing information
        - Broken demonstration
        - Other
    validations:
      required: true

  - type: textarea
    attributes:
      label: Issue description
      description: |
        Let us know what went wrong when you were using this documentation and what we could do to improve it.
      value: |
        I was looking for ... and it appears that ...

  - type: textarea
    attributes:
      label: Context 🔦
      description: What are you trying to accomplish? What brought you to this page? Your context can help us to come up with solutions that benefit the community as a whole.
