name: Deploy PROD Inbound Mail

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_inbound_mail:
    strategy:
      matrix:
        name: ['novu/inbound-mail-ee', 'novu/inbound-mail']
    uses: ./.github/workflows/reusable-inbound-mail-e2e.yml
    with:
      ee: ${{ contains (matrix.name,'-ee') }}
    secrets: inherit

  build_prod_image:
    runs-on: ubuntu-latest
    timeout-minutes: 80
    environment: Production
    outputs:
      docker_image: ${{ steps.build-image.outputs.IMAGE }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    strategy:
      matrix:
        name: ['novu/inbound-mail-ee', 'novu/inbound-mail']
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-project

      - name: build api
        run: pnpm build:inbound-mail

      - uses: crazy-max/ghaction-setup-docker@v2
        with:
          version: v24.0.6
          daemon-config: |
            {
              "features": {
                "containerd-snapshotter": true
              }
            }

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Prepare
        shell: bash
        run: |
          service=${{ matrix.name }}
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Set Bull MQ Env variable for EE
        if: contains(matrix.name, 'ee')
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: ${{matrix.name}}
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_BUILD_ARGUMENTS: >
            --cache-from type=registry,ref=ghcr.io/novuhq/cache:build-cache-${{ env.SERVICE_NAME }}-prod
            --cache-to type=registry,ref=ghcr.io/novuhq/cache:build-cache-${{ env.SERVICE_NAME }}-prod,mode=max
            --platform=linux/amd64
            --output=type=image,name=ghcr.io/novuhq/${{ matrix.name }},push-by-digest=true,name-canonical=true
        run: |
          echo $GH_PASSWORD | docker login ghcr.io -u $GH_ACTOR --password-stdin
          cd apps/inbound-mail && pnpm --silent --workspace-root pnpm-context -- apps/inbound-mail/Dockerfile | BULL_MQ_PRO_NPM_TOKEN=${BULL_MQ_PRO_NPM_TOKEN} docker buildx build --secret id=BULL_MQ_PRO_NPM_TOKEN --build-arg PACKAGE_PATH=apps/inbound-mail - -t novu-inbound-mail --load $DOCKER_BUILD_ARGUMENTS
          docker tag novu-inbound-mail ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:latest
          docker tag novu-inbound-mail ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag novu-inbound-mail ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$IMAGE_TAG

          docker run --network=host --name inbound-mail -dit --env NODE_ENV=test ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$IMAGE_TAG

          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:latest
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$IMAGE_TAG
          echo "IMAGE=ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy_prod_inbound_mail_eu:
    needs:
      - build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: inbound_mail
      terraform_workspace: novu-prod-eu
      docker_image: ghcr.io/novuhq/novu/inbound-mail-ee:${{ github.sha }}

  deploy_prod_inbound_mail_us:
    needs:
      - build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: inbound_mail
      terraform_workspace: novu-prod
      docker_image: ghcr.io/novuhq/novu/inbound-mail-ee:${{ github.sha }}
      deploy_sentry_release: true
      sentry_project: inbound-mail
