name: Prepare Self-hosted Release

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
  workflow_dispatch:

permissions:
  contents: write
  packages: write
  deployments: write
  id-token: write

jobs:
  build_docker:
    runs-on: ubuntu-latest
    timeout-minutes: 90
    strategy:
      fail-fast: false
      matrix:
        name: ['novu/api', 'novu/worker', 'novu/web', 'novu/webhook', 'novu/ws']
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Variables
        shell: bash
        run: |
          service=${{ matrix.name }}
          LATEST_VERSION=$(jq -r '.version' lerna.json)
          SERVICE_NAME=$(basename "${service//-/-}")
          SERVICE_COMMON_NAME=$(echo "$SERVICE_NAME" | sed 's/-ee$//')
          echo "LATEST_VERSION=$LATEST_VERSION" >> $GITHUB_ENV
          echo "SERVICE_NAME=$SERVICE_NAME" >> $GITHUB_ENV
          echo "SERVICE_COMMON_NAME=$SERVICE_COMMON_NAME" >> $GITHUB_ENV
          echo "REGISTRY_OWNER=novuhq" >> $GITHUB_ENV
          echo "This is the service name: $SERVICE_NAME and release version: $LATEST_VERSION"

      - name: Install pnpm
        uses: pnpm/action-setup@v3

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.8.1
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup Docker
        uses: crazy-max/ghaction-setup-docker@v2
        with:
          version: v24.0.6
          daemon-config: |
            {
              "features": {
                "containerd-snapshotter": true
              }
            }

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - uses: ./.github/actions/free-space
        name: Extend space in Action Container

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build ${{ env.SERVICE_NAME }} Community Docker Image
        shell: bash
        env:
          DOCKER_BUILD_ARGUMENTS: >
            --cache-from type=registry,ref=ghcr.io/${{ env.REGISTRY_OWNER }}/cache:build-cache-${{ env.SERVICE_NAME }}-community
            --cache-to type=registry,ref=ghcr.io/${{ env.REGISTRY_OWNER }}/cache:build-cache-${{ env.SERVICE_NAME }}-community,mode=max
            --platform=linux/amd64,linux/arm64 --provenance=false
            --output=type=image,name=ghcr.io/${{ env.REGISTRY_OWNER }}/${{ env.SERVICE_NAME }},push-by-digest=true,name-canonical=true
        run: |
          cd apps/$SERVICE_COMMON_NAME

          if [ "${{ env.SERVICE_NAME }}" == "worker" ]; then
            cd src/ && echo -e "\nIS_SELF_HOSTED=true\nOS_TELEMETRY_URL=\"${{ secrets.OS_TELEMETRY_URL }}\"" >> .example.env && cd ..
          elif [ "${{ env.SERVICE_NAME }}" == "web" ]; then
            echo -e "\nIS_V2_ENABLED=true" >> .env.sample
          fi

          pnpm run docker:build
          docker images

      - name: Check for EE files
        id: check-ee-files
        run: |
          patterns=(
            './node_modules/@novu/ee-**/dist/index.js'
            './node_modules/@taskforcesh/bullmq-pro'  # Add more patterns as needed
          )
          for pattern in "${patterns[@]}"; do
            if docker run --rm novu-$SERVICE_COMMON_NAME sh -c "ls $pattern 2>/dev/null"; then
              echo "::error::'$pattern' files were detected in ${{ matrix.name }}."
              exit 1
            fi
          done
          echo "No matching EE files found in the Docker image ${{ matrix.name }}"

      - name: Tag and Push docker image
        shell: bash
        run: |
          docker tag novu-$SERVICE_COMMON_NAME ghcr.io/$REGISTRY_OWNER/${{ matrix.name }}:${{ env.LATEST_VERSION }}
          docker tag novu-$SERVICE_COMMON_NAME ghcr.io/$REGISTRY_OWNER/${{ matrix.name }}:latest
          docker push ghcr.io/$REGISTRY_OWNER/${{ matrix.name }}:${{ env.LATEST_VERSION }}
          docker push ghcr.io/$REGISTRY_OWNER/${{ matrix.name }}:latest
