name: Deploy PROD WEBHOOK

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  publish_docker_image_webhook:
    uses: ./.github/workflows/reusable-docker.yml
    with:
      environment: Production
      package_name: novu/webhook
      project_path: apps/webhook
      test_port: 1341
      health_check: true
      local_tag: novu-webhook
      env_tag: prod
      aws-region: us-east-1

    secrets: inherit

  deploy_prod_webhook_eu:
    needs:
      - publish_docker_image_webhook
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: webhook
      terraform_workspace: novu-prod-eu
      docker_image: ${{ needs.publish_docker_image_webhook.outputs.docker_image_ee }}

  deploy_prod_webhook_us:
    needs:
      - publish_docker_image_webhook
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: webhook
      terraform_workspace: novu-prod
      docker_image: ${{ needs.publish_docker_image_webhook.outputs.docker_image_ee }}
