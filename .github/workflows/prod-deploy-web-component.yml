name: Deploy PROD Notification Center Web Component

on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  build:
    uses: ./.github/workflows/reusable-notification-center.yml

  deploy_web_component:
    needs: build
    runs-on: ubuntu-latest
    timeout-minutes: 80
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    environment: Production

    steps:
      - uses: actions/checkout@v4

      - name: Download the artifact
        uses: actions/download-artifact@v4
        with:
          name: notification-center-web-component
          path: packages/notification-center/dist/umd

      - name: Deploy Notification Center Web Component to PROD
        uses: scopsy/actions-netlify@develop
        with:
          publish-dir: packages/notification-center/dist/umd
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: prod
          production-deploy: true
          alias: prod
          github-deployment-environment: Production
          github-deployment-description: Notification Center Web Component Deployment
          netlify-config-path: packages/notification-center/netlify.toml
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: 468165e2-bd64-4f33-9fd9-4b93ef8a0be0
        timeout-minutes: 1
