name: 'Prepare Cloud Release'

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  workflow_dispatch:
  # Triggers the workflow every work day at 8:00 UTC
  # The 3 hour offset should change when daylight savings change for GMT +3.
  schedule:
    - cron: '0 8 * * 1,2,3,4,5'

jobs:
  prepare-cloud-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    timeout-minutes: 10
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set output variables
        id: output-variables
        run: |
          echo "date_humanized=$(date +'%Y-%m-%d %H:%M')" >> "$GITHUB_OUTPUT"
          echo "branch_name=release_$(date +'%Y_%m_%d_%H_%M')" >> "$GITHUB_OUTPUT"

      - name: Create Novu Cloud release branch
        run: git checkout -b ${{ steps.output-variables.outputs.branch_name }}

      - name: Push release branch
        run: git push origin ${{ steps.output-variables.outputs.branch_name }}

      - name: Create Novu Cloud release PR
        id: create-pr
        run: |
          echo "pr_url=$(gh pr create --base prod --head ${{steps.output-variables.outputs.branch_name}} --title 'chore(root): Release ${{steps.output-variables.outputs.date_humanized}}' --body 'Automated daily production Novu Cloud release')" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Enable PR automerge
        id: enable-pr-automerge
        if: ${{ steps.create-pr.outputs.pr_url != '' }}
        run: |
          gh pr merge --auto -m ${{steps.create-pr.outputs.pr_url}}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete release branch step on failure
        id: delete-branch
        if: ${{ failure() || steps.create-pr.outputs.pr_url == '' }}
        run: |
          git push origin -d ${{ steps.output-variables.outputs.branch_name }}

      - name: Generate commit log
        id: commit-log
        if: ${{ success() }}
        run: |
          echo 'commit_log<<EOF' >> $GITHUB_OUTPUT
          echo $(git log --format="format:%h %s (@%aL)\n" origin/prod..origin/${{steps.output-variables.outputs.branch_name}} | sed "s/\"/'/g") >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

      - name: Send commit log to Slack
        id: slack
        uses: slackapi/slack-github-action@v1.26.0
        if: ${{ success() && steps.commit-log.outputs.commit_log != '' }}
        with:
          payload: |
            {
              "text": "*<${{steps.create-pr.outputs.pr_url}}|Novu Cloud Release: ${{steps.output-variables.outputs.date_humanized }}>*\n```${{steps.commit-log.outputs.commit_log}}```",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{steps.create-pr.outputs.pr_url}}|Novu Cloud Release: ${{steps.output-variables.outputs.date_humanized }}>*\n```${{steps.commit-log.outputs.commit_log}}```"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL_ENG_FEED_DEPLOYMENTS}}
