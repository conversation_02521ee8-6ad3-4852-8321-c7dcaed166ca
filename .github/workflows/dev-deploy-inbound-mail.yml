name: Deploy DEV Inbound Mail

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'apps/inbound-mail/**'
      - 'packages/shared/**'
      - 'libs/testing/**'
env:
  TF_WORKSPACE: novu-dev

jobs:
  test_inbound_mail:
    strategy:
      matrix:
        name: ['novu/inbound-mail-ee', 'novu/inbound-mail']
    uses: ./.github/workflows/reusable-inbound-mail-e2e.yml
    with:
      ee: ${{ contains (matrix.name,'-ee') }}
    secrets: inherit

  dev_deploy_inbound_mail:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    needs: test_inbound_mail
    timeout-minutes: 80
    environment: Development
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    strategy:
      matrix:
        name: ['novu/inbound-mail-ee']

    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-project

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Prepare
        shell: bash
        run: |
          service=${{ matrix.name }}
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Set Bull MQ Env variable for EE
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV
        if: ${{contains(matrix.name, 'ee')}}

      - name: Build with Buildx, tag, and test
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/inbound-mail
          IMAGE_TAG: ${{ github.sha }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64 --provenance=false
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          set -x
          cd apps/inbound-mail && pnpm run docker:build

      - name: Tag and test
        id: build-image
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/inbound-mail
          IMAGE_TAG: ${{ github.sha }}
        run: |
          echo "Built image"
          docker tag novu-inbound-mail $REGISTRY/$REPOSITORY:$IMAGE_TAG

          docker run --network=host --name inbound-mail -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Push PR tag image
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/inbound-mail
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Checkout cloud infra
        uses: actions/checkout@master
        with:
          repository: novuhq/cloud-infra
          token: ${{ secrets.GH_PACKAGES }}
          path: cloud-infra

      - name: Terraform setup
        uses: hashicorp/setup-terraform@v3
        with:
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}
          terraform_version: 1.5.5
          terraform_wrapper: false

      - name: Terraform Init
        working-directory: cloud-infra/terraform/novu/aws
        run: terraform init

      - name: Terraform get output
        working-directory: cloud-infra/terraform/novu/aws
        id: terraform
        run: |
          echo "inbound_mail_ecs_container_name=$(terraform output -json inbound_mail_ecs_container_name | jq -r .)" >> $GITHUB_ENV
          echo "inbound_mail_ecs_service=$(terraform output -json inbound_mail_ecs_service | jq -r .)" >> $GITHUB_ENV
          echo "inbound_mail_ecs_cluster=$(terraform output -json inbound_mail_ecs_cluster | jq -r .)" >> $GITHUB_ENV
          echo "inbound_mail_task_name=$(terraform output -json inbound_mail_task_name | jq -r .)" >> $GITHUB_ENV

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.inbound_mail_task_name }} \
          --query taskDefinition > task-definition.json

      - name: Render Amazon ECS task definition
        id: render-web-container
        uses: aws-actions/amazon-ecs-render-task-definition@39c13cf530718ffeb524ec8ee0c15882bcb13842
        with:
          task-definition: task-definition.json
          container-name: ${{ env.inbound_mail_ecs_container_name }}
          image: ${{ steps.build-image.outputs.IMAGE }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-render-task-definition@39c13cf530718ffeb524ec8ee0c15882bcb13842
        with:
          task-definition: ${{ steps.render-web-container.outputs.task-definition }}
          service: ${{ env.inbound_mail_ecs_service }}
          cluster: ${{ env.inbound_mail_ecs_cluster }}
          wait-for-service-stability: true

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@main
        with:
          path: apps/inbound-mail

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: novu-r9
          SENTRY_PROJECT: inbound-mail
        with:
          version: ${{ steps.package-version.outputs.current-version}}
          environment: dev
          version_prefix: v
          sourcemaps: apps/inbound-mail/dist
          ignore_empty: true
          ignore_missing: true
          url_prefix: '~'
