name: E2E worker Tests

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_call:
    inputs:
      ee:
        description: 'use the ee version of worker'
        required: false
        default: false
        type: boolean

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  e2e_worker_service:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80

    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - id: setup
        run: |
          if ! [[ -z "${{ secrets.SUBMODULES_TOKEN }}" ]]; then
             echo "has_token=true" >> $GITHUB_OUTPUT
          else
             echo "has_token=false" >> $GITHUB_OUTPUT
          fi
      # checkout with submodules if token is provided
      - uses: actions/checkout@v4
        if: steps.setup.outputs.has_token == 'true'
        with:
          submodules: ${{ inputs.ee }}
          token: ${{ secrets.SUBMODULES_TOKEN }}
        # else checkout without submodules if the token is not provided
      - uses: actions/checkout@v4
        if: steps.setup.outputs.has_token != 'true'

      - uses: ./.github/actions/setup-project

      - uses: ./.github/actions/setup-redis-cluster

      - uses: ./.github/actions/start-localstack

        # Runs a single command using the runners shell
      - name: Build worker
        run: CI='' pnpm build:worker --skip-nx-cache

      # Runs a set of commands using the runners shell
      - name: Run a test
        run: |
          cd apps/worker && pnpm test:e2e
          pnpm test
