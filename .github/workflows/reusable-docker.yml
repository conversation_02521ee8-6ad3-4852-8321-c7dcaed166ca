name: Build, tag and push docker image to ghcr.io

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_call:
    inputs:
      # The environment with the GH secrets environment.
      environment:
        required: true
        type: string
      # The name of the package under which the docker image will be published on GH Packages.
      package_name:
        required: true
        type: string
      # The path to the project in the monorepo.
      project_path:
        required: true
        type: string
      # The port used for testing. This is not a required input, and it defaults to '1341'.
      # This value is added, so you can test the image after it's built in test mode and by doing a health-check.
      test_port:
        required: false
        default: '1341'
        type: string
      # The boolean that helps to determine whether to perform a health check. This is not a required input and defaults to false.
      health_check:
        required: false
        default: false
        type: boolean
      # The tag under which the image is built, it should align with the name in the project command docker:build
      local_tag:
        required: true
        type: string
      # The environment tag. Possible values are dev, stg, and prod. This is not a required input of type string.
      env_tag:
        required: false
        type: string
      aws-region:
        description: 'Region for AWS'
        required: true
        type: string

    outputs:
      docker_image:
        description: 'The image that was built'
        value: ${{ jobs.reusable_docker.outputs.docker_image }}
      docker_image_ee:
        description: 'The enterprise image that was built'
        value: ${{ jobs.reusable_docker.outputs.docker_image_ee }}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  reusable_docker:
    runs-on: ubuntu-latest
    timeout-minutes: 80
    environment: ${{ inputs.environment }}
    outputs:
      docker_image: ${{ steps.save-image-to-output.outputs.IMAGE }}
      docker_image_ee: ${{ steps.save-image-to-output.outputs.IMAGE_EE }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    strategy:
      matrix:
        name: ['${{ inputs.package_name }}-ee']
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: ${{ contains (matrix.name,'-ee') }}
          token: ${{ secrets.SUBMODULES_TOKEN }}

      - name: Prepare
        shell: bash
        run: |
          service=${{ matrix.name }}
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - uses: ./.github/actions/setup-project
        with:
          slim: 'true'
          submodules: ${{ contains (matrix.name,'-ee') }}

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ inputs.aws-region }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to ECR
        id: build-image
        if: ${{ inputs.env_tag == 'dev' || inputs.env_tag == 'stg' }}
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ inputs.package_name }}
          DOCKER_NAME: ${{ matrix.name }}
          LOCAL_TAG: ${{ inputs.local_tag }}
          IMAGE_TAG: ${{ github.sha }}
          ENV_TAG: ${{ inputs.env_tag }}
          PROJECT_PATH: ${{ inputs.project_path }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64 --provenance=false
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          cd $PROJECT_PATH && npm run docker:build
          docker tag $LOCAL_TAG $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker tag $LOCAL_TAG $REGISTRY/$REPOSITORY:$ENV_TAG
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker push $REGISTRY/$REPOSITORY:$ENV_TAG

      - name: Production build, tag, and push image to ECR
        id: build-prod-image
        if: ${{ inputs.env_tag == 'prod' }}
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ inputs.package_name }}
          DOCKER_NAME: ${{ matrix.name }}
          LOCAL_TAG: ${{ inputs.local_tag }}
          IMAGE_TAG: ${{ github.sha }}
          ENV_TAG: ${{ inputs.env_tag }}
          PROJECT_PATH: ${{ inputs.project_path }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          cd $PROJECT_PATH && npm run docker:build
          docker tag $LOCAL_TAG $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker tag $LOCAL_TAG $REGISTRY/$REPOSITORY:$ENV_TAG
          docker tag $LOCAL_TAG $REGISTRY/$REPOSITORY:latest
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker push $REGISTRY/$REPOSITORY:$ENV_TAG
          docker push $REGISTRY/$REPOSITORY:latest

      - name: Save image to output
        id: save-image-to-output
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ inputs.package_name }}
          DOCKER_NAME: ${{ matrix.name }}
          IMAGE_TAG: ${{ github.sha }}
          OUTPUT_NAME: ${{ contains(matrix.name,'-ee') && 'IMAGE_EE' || 'IMAGE' }}
        run: |
          echo "$OUTPUT_NAME=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Health check test
        id: health-check
        if: ${{ inputs.health_check == 'true' && (steps.build-image.outcome == 'success' || steps.build-prod-image.outcome == 'success') }}
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ inputs.package_name }}
          LOCAL_TAG: ${{ inputs.local_tag }}
          IMAGE_TAG: ${{ github.sha }}
          TEST_PORT: ${{ inputs.test_port }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
        run: |
          docker run --network=host --name $LOCAL_TAG -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:$TEST_PORT/v1/health-check | grep 'ok'
