name: Deploy DEV WS

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'apps/ws/**'
env:
  TF_WORKSPACE: novu-dev
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_ws:
    uses: ./.github/workflows/reusable-ws-e2e.yml
    with:
      ee: true
    secrets: inherit

  # This workflow contains a single job called "build"
  deploy_ws:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    needs: test_ws
    timeout-minutes: 80
    environment: Development
    if: "!contains(github.event.head_commit.message, 'ci skip')"

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Prepare
        shell: bash
        run: |
          service="novu/ws-ee"
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Checkout cloud infra
        uses: actions/checkout@master
        with:
          repository: novuhq/cloud-infra
          token: ${{ secrets.GH_PACKAGES }}
          path: cloud-infra

      - name: Terraform setup
        uses: hashicorp/setup-terraform@v3
        with:
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}
          terraform_version: 1.5.5
          terraform_wrapper: false
      - name: Terraform Init
        working-directory: cloud-infra/terraform/novu/aws
        run: terraform init

      - name: Terraform get output
        working-directory: cloud-infra/terraform/novu/aws
        id: terraform
        run: |
          echo "ws_ecs_container_name=$(terraform output -json ws_ecs_container_name | jq -r .)" >> $GITHUB_ENV
          echo "ws_ecs_service=$(terraform output -json ws_ecs_service | jq -r .)" >> $GITHUB_ENV
          echo "ws_ecs_cluster=$(terraform output -json ws_ecs_cluster | jq -r .)" >> $GITHUB_ENV
          echo "ws_task_name=$(terraform output -json ws_task_name | jq -r .)" >> $GITHUB_ENV

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.ws_task_name }} \
          --query taskDefinition > task-definition.json

      - name: Set Bull MQ Env variable for EE
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV

      - name: Build with Buildx, tag, and test
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/ws
          IMAGE_TAG: ${{ github.sha }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64 --provenance=false
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          set -x
          cd apps/ws && pnpm run docker:build

      - name: Tag and test
        id: build-image
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/ws
          IMAGE_TAG: ${{ github.sha }}
        run: |
          echo "Built image"
          docker tag novu-ws $REGISTRY/$REPOSITORY:$IMAGE_TAG

          docker run --network=host --name ws -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:1340/v1/health-check | grep 'ok'
          echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Push PR tag image
        shell: bash
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu-dev/ws
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Render Amazon ECS task definition
        id: render-container
        uses: aws-actions/amazon-ecs-render-task-definition@39c13cf530718ffeb524ec8ee0c15882bcb13842
        with:
          task-definition: task-definition.json
          container-name: ${{ env.ws_ecs_container_name }}
          image: ${{ steps.build-image.outputs.IMAGE }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@3e7310352de91b71a906e60c22af629577546002
        with:
          task-definition: ${{ steps.render-container.outputs.task-definition }}
          service: ${{ env.ws_ecs_service }}
          cluster: ${{ env.ws_ecs_cluster }}
          wait-for-service-stability: true
