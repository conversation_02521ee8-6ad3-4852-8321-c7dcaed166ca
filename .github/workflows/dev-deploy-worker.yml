name: Deploy DEV Worker

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'apps/api/**'
      - 'libs/dal/**'
      - 'packages/shared/**'
      - 'libs/testing/**'
      - 'libs/application-generic/**'
      - 'packages/stateless/**'
      - 'packages/node/**'

env:
  TF_WORKSPACE: novu-dev
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_worker:
    uses: ./.github/workflows/reusable-worker-e2e.yml
    with:
      ee: true
    secrets: inherit

  build_dev_worker:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    needs: test_worker
    timeout-minutes: 80
    environment: Development
    outputs:
      docker_image: ${{ steps.docker_build.outputs.IMAGE }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - uses: ./.github/actions/docker/build-worker
        id: docker_build
        with:
          tag: dev
          push: 'true'
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          docker_name: 'novu/worker-ee'
          bullmq_secret: ${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}
          environment: dev

  deploy_dev_workers:
    needs: build_dev_worker
    uses: ./.github/workflows/reusable-workers-service-deploy.yml
    secrets: inherit
    with:
      environment: Development
      terraform_workspace: novu-dev
      docker_image: ${{ needs.build_dev_worker.outputs.docker_image }}

  newrelic:
    runs-on: ubuntu-latest
    name: New Relic Deploy
    needs: deploy_dev_workers
    environment: Development
    steps:
      # This step builds a var with the release tag value to use later
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      # This step creates a new Change Tracking Marker
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          region: EU
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: 'MzgxMjQwOHxBUE18QVBQTElDQVRJT058NDk3NjQzODIy'
          version: '${{ env.RELEASE_VERSION }}'
          user: '${{ github.actor }}'
