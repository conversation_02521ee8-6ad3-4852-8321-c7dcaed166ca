name: Deploy PROD Worker

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  build_prod_image:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80
    environment: Production
    outputs:
      docker_image: ${{ steps.build-image.outputs.IMAGE }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - name: build worker
        run: pnpm build:worker --skip-nx-cache

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Prepare
        shell: bash
        run: |
          service="novu/worker-ee"
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Set Bull MQ Env variable for EE
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID}}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu/worker
          IMAGE_TAG: ${{ github.sha }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          cd apps/worker && pnpm --silent --workspace-root pnpm-context -- apps/worker/Dockerfile | BULL_MQ_PRO_NPM_TOKEN=${BULL_MQ_PRO_NPM_TOKEN} docker buildx build --secret id=BULL_MQ_PRO_NPM_TOKEN --build-arg PACKAGE_PATH=apps/worker - -t novu-worker --load $DOCKER_BUILD_ARGUMENTS
          docker tag novu-worker $REGISTRY/$REPOSITORY:latest
          docker tag novu-worker $REGISTRY/$REPOSITORY:prod
          docker tag novu-worker $REGISTRY/$REPOSITORY:$IMAGE_TAG

          docker run --network=host --name worker -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:1342/v1/health-check | grep 'ok'

          docker push $REGISTRY/$REPOSITORY:prod
          docker push $REGISTRY/$REPOSITORY:latest
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy_prod_workers_eu:
    needs: build_prod_image
    uses: ./.github/workflows/reusable-workers-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      terraform_workspace: novu-prod-eu
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}

  deploy_prod_workers_us:
    needs: build_prod_image
    uses: ./.github/workflows/reusable-workers-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      terraform_workspace: novu-prod
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}
      deploy_sentry_release: true
      sentry_project: worker

  newrelic:
    runs-on: ubuntu-latest
    name: New Relic Deploy
    needs: deploy_prod_workers_us
    environment: Production
    steps:
      # This step builds a var with the release tag value to use later
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      # This step creates a new Change Tracking Marker
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          region: EU
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: 'MzgxMjQwOHxBUE18QVBQTElDQVRJT058NDk3NzA2ODk2'
          version: '${{ env.RELEASE_VERSION }}'
          user: '${{ github.actor }}'
