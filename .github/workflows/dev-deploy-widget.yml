name: Deploy DEV Widget

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'apps/widget/**'
      - 'apps/ws/**'
      - 'packages/shared/**'

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  test_widget:
    uses: ./.github/workflows/reusable-widget-e2e.yml
    with:
      ee: true
    secrets: inherit

  deploy_widget:
    needs: test_widget
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    uses: ./.github/workflows/reusable-widget-deploy.yml
    with:
      environment: Development
      react_app_api_url: https://api.novu-staging.co
      react_app_ws_url: https://dev.ws.novu.co
      react_app_webhook_url: https://dev.webhook.novu.co
      react_app_sentry_dsn: https://<EMAIL>/625116
      react_app_environment: dev
      netlify_deploy_message: Dev deployment
      netlify_alias: dev
      netlify_gh_env: development
      netlify_site_id: b9147448-b835-4eb1-a2f0-11102f611f5f
    secrets: inherit

  publish_docker_image_widget:
    needs: test_widget
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    uses: ./.github/workflows/reusable-docker.yml
    with:
      environment: Development
      package_name: novu/widget
      project_path: apps/widget
      local_tag: novu-widget
      env_tag: dev
    secrets: inherit
