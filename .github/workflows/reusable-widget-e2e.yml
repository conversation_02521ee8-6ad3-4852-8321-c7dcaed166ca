name: Test E2E WIDGET

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_call:
    inputs:
      ee:
        description: 'use the ee version of worker'
        required: false
        default: false
        type: boolean

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  e2e_widget:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - id: setup
        run: |
          if ! [[ -z "${{ secrets.SUBMODULES_TOKEN }}" ]]; then
            echo "has_token=true" >> $GITHUB_OUTPUT
          else
            echo "has_token=false" >> $GITHUB_OUTPUT
          fi
      # checkout with submodules if token is provided
      - uses: actions/checkout@v4
        if: steps.setup.outputs.has_token == 'true'
        with:
          submodules: ${{ inputs.ee }}
          token: ${{ secrets.SUBMODULES_TOKEN }}
      # else checkout without submodules if the token is not provided
      - uses: actions/checkout@v4
        if: steps.setup.outputs.has_token != 'true'

      - uses: ./.github/actions/setup-project
        id: setup-project
        with:
          cypress_version: 12.9.0
          cypress: true
          submodules: ${{ inputs.ee && steps.setup.outputs.has_token == 'true' }}

      - uses: ./.github/actions/setup-redis-cluster

      - uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build
          args: --skip-nx-cache
          projects: '@novu/widget,@novu/embed,@novu/api-service,@novu/worker,@novu/ws'

      - uses: ./.github/actions/run-backend
        with:
          cypress_github_oauth_client_id: ${{ secrets.CYPRESS_GITHUB_OAUTH_CLIENT_ID }}
          cypress_github_oauth_client_secret: ${{ secrets.CYPRESS_GITHUB_OAUTH_CLIENT_SECRET }}
          launch_darkly_sdk_key: ${{ secrets.LAUNCH_DARKLY_SDK_KEY }}
          ci_ee_test: ${{ steps.setup.outputs.has_token }}

      # Runs a single command using the runners shell
      - name: Start Client
        run: pnpm start:widget:test &

      - name: Start WS
        env:
          NODE_ENV: 'test'
        run: |
          cp apps/ws/src/.env.test apps/ws/dist/.env.test
          cd apps/ws && pnpm start:prod &

      - name: Wait on Widget and WS Services
        run: wait-on --timeout=180000 http://127.0.0.1:1340/v1/health-check http://127.0.0.1:3500/

      - name: Cypress install
        if: steps.setup-project.outputs.cypress_cache_hit != 'true'
        working-directory: apps/widget
        run: pnpm cypress install

      - name: Cypress run
        uses: cypress-io/github-action@v6
        env:
          NODE_ENV: test
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_WIDGET_KEY }}
        with:
          working-directory: apps/widget
          wait-on: http://127.0.0.1:3500/
          browser: chrome
          install: false
          record: true
          parallel: false
          config-file: cypress.config.ts

      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: apps/widget/cypress/screenshots
      # Test run video was always captured, so this action uses "always()" condition
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos
          path: apps/widget/cypress/videos

      - name: Send Slack notifications
        uses: ./.github/actions/slack-notify-on-failure
        if: failure()
        with:
          slackWebhookURL: ${{ secrets.SLACK_WEBHOOK_URL_ENG_FEED_GITHUB }}
