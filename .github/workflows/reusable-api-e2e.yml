name: E2E API Tests

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_call:
    inputs:
      test-e2e-affected:
        description: 'detect if we should run e2e tests'
        required: false
        default: true
        type: boolean
      test-e2e-ee-affected:
        description: 'detect if we should run e2e-ee tests'
        required: false
        default: true
        type: boolean
      test-unit:
        description: 'detect if we should run unit tests'
        required: false
        default: true
        type: boolean
      ee:
        description: 'use the ee version of api'
        required: false
        default: false
        type: boolean
      job-name:
        description: 'job name [options: novu/api-ee, novu/api]'
        required: true
        type: string

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  check_submodule_token:
    name: Check if the secret exists or not.
    runs-on: ubuntu-latest
    outputs:
      has_token: ${{ steps.secret-check.outputs.has_token }}
    steps:
      - name: Check if secret exists
        id: secret-check
        run: |
          if [[ -n "${{ secrets.SUBMODULES_TOKEN }}" ]]; then
            echo "::set-output name=has_token::true"
          else
            echo "::set-output name=has_token::false"
          fi

  # This workflow contains a single job called "build"
  e2e_api:
    name: Test E2E
    if: ${{ (contains(inputs.job-name, '-ee') && inputs.test-e2e-ee-affected) || (!contains(inputs.job-name, '-ee') && inputs.test-e2e-affected) }}
    runs-on: ubuntu-latest
    timeout-minutes: 80
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    needs: [check_submodule_token]
    steps:
      # checkout with submodules if token is provided
      - uses: actions/checkout@v4
        name: Checkout with submodules
        if: ${{ needs.check_submodule_token.outputs.has_token == 'true' && inputs.ee }}
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}

      # else checkout without submodules if the token is not provided
      - uses: actions/checkout@v4
        name: Checkout
        if: ${{ needs.check_submodule_token.outputs.has_token != 'true' || !contains (inputs.job-name,'-ee') }}

      - uses: ./.github/actions/setup-project
        name: Setup project
        with:
          submodules: ${{ inputs.ee && needs.check_submodule_token.outputs.has_token == 'true' }}

      - uses: ./.github/actions/setup-redis-cluster
        name: Setup redis cluster

      - uses: ./.github/actions/start-localstack
        name: Start localstack

      - uses: ./.github/actions/run-worker
        name: Run worker
        with:
          launch_darkly_sdk_key: ${{ secrets.LAUNCH_DARKLY_SDK_KEY }}
          ee: ${{ needs.check_submodule_token.outputs.has_token == 'true' && inputs.ee }}

        # Runs a single command using the runners shell
      - name: Build API
        run: CI='' pnpm build:api --skip-nx-cache

      - name: Run E2E tests
        if: ${{ !inputs.ee }}
        env:
          LAUNCH_DARKLY_SDK_KEY: ${{ secrets.LAUNCH_DARKLY_SDK_KEY }}
        run: |
          cd apps/api && pnpm test:e2e

      - name: Run E2E EE tests
        if: ${{ needs.check_submodule_token.outputs.has_token == 'true' && inputs.ee }}
        env:
          LAUNCH_DARKLY_SDK_KEY: ${{ secrets.LAUNCH_DARKLY_SDK_KEY }}
          CI_EE_TEST: true
          CLERK_ENABLED: true
          CLERK_ISSUER_URL: ${{ vars.CLERK_ISSUER_URL }}
          CLERK_SECRET_KEY: ${{ secrets.CLERK_SECRET_KEY }}
          CLERK_PRIVATE_KEY_BASE64: ${{ secrets.CLERK_PRIVATE_KEY_BASE64 }}
          CLERK_PEM_PUBLIC_KEY_BASE64: ${{ secrets.CLERK_PEM_PUBLIC_KEY_BASE64 }}
          CLERK_WEBHOOK_SECRET: ${{ secrets.CLERK_WEBHOOK_SECRET }}
          CLERK_LONG_LIVED_TOKEN: ${{ secrets.CLERK_LONG_LIVED_TOKEN }}
        run: |
          export CLERK_PEM_PUBLIC_KEY=$(echo $CLERK_PEM_PUBLIC_KEY_BASE64 | base64 -d)
          export CLERK_PRIVATE_KEY=$(echo $CLERK_PRIVATE_KEY_BASE64 | base64 -d)
          cd apps/api && pnpm test:e2e:ee

      - name: Kill port for worker 1342 for unit tests
        run: sudo kill -9 $(sudo lsof -t -i:1342)

      - name: Run unit tests
        if: inputs.test-unit
        run: |
          cd apps/api && pnpm test

      - name: Send Slack notifications
        uses: ./.github/actions/slack-notify-on-failure
        if: failure()
        with:
          slackWebhookURL: ${{ secrets.SLACK_WEBHOOK_URL_ENG_FEED_GITHUB }}
