name: Deploy DEV API

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'apps/api/**'
      - 'libs/dal/**'
      - 'packages/shared/**'
      - 'libs/application-generic/**'
env:
  TF_WORKSPACE: novu-dev
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_api:
    uses: ./.github/workflows/reusable-api-e2e.yml
    with:
      ee: true
      job-name: 'novu/api-ee'
    secrets: inherit

  deploy_dev_api:
    runs-on: ubuntu-latest
    needs: test_api
    timeout-minutes: 80
    environment: Development
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - uses: ./.github/actions/docker/build-api
        id: docker_build
        with:
          tag: dev
          push: 'true'
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          docker_name: 'novu/api-ee'
          bullmq_secret: ${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}
          environment: dev

      - name: Checkout cloud infra
        uses: actions/checkout@master
        with:
          repository: novuhq/cloud-infra
          token: ${{ secrets.GH_PACKAGES }}
          path: cloud-infra

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-2

      - name: Terraform setup
        uses: hashicorp/setup-terraform@v3
        with:
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}
          terraform_version: 1.5.5
          terraform_wrapper: false

      - name: Terraform Init
        working-directory: cloud-infra/terraform/novu/aws
        run: terraform init

      - name: Terraform get output
        working-directory: cloud-infra/terraform/novu/aws
        id: terraform
        run: |
          echo "api_ecs_container_name=$(terraform output -json api_ecs_container_name | jq -r .)" >> $GITHUB_ENV
          echo "api_ecs_service=$(terraform output -json api_ecs_service | jq -r .)" >> $GITHUB_ENV
          echo "api_ecs_cluster=$(terraform output -json api_ecs_cluster | jq -r .)" >> $GITHUB_ENV
          echo "api_task_name=$(terraform output -json api_task_name | jq -r .)" >> $GITHUB_ENV

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.api_task_name }} \
          --query taskDefinition > task-definition.json

      - name: Render Amazon ECS task definition
        id: render-web-container
        uses: aws-actions/amazon-ecs-render-task-definition@39c13cf530718ffeb524ec8ee0c15882bcb13842
        with:
          task-definition: task-definition.json
          container-name: ${{ env.api_ecs_container_name }}
          image: ${{ steps.docker_build.outputs.image }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@3e7310352de91b71a906e60c22af629577546002
        with:
          task-definition: ${{ steps.render-web-container.outputs.task-definition }}
          service: ${{ env.api_ecs_service }}
          cluster: ${{ env.api_ecs_cluster }}
          wait-for-service-stability: true

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@main
        with:
          path: apps/api

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: novu-r9
          SENTRY_PROJECT: api
        with:
          version: ${{ steps.package-version.outputs.current-version}}
          environment: dev
          version_prefix: v
          sourcemaps: apps/api/dist
          ignore_empty: true
          ignore_missing: true
          url_prefix: '~'

  novu_sync:
    runs-on: ubuntu-latest
    name: Sync Novu State
    needs: deploy_dev_api
    environment: Development
    steps:
      # https://github.com/novuhq/actions-novu-sync
      - name: Sync State to Novu
        uses: novuhq/actions-novu-sync@v2
        with:
          # The secret key used to authenticate with Novu Cloud
          # To get the secret key, go to https://web.novu.co/api-keys.
          # Required.
          secret-key: ${{ secrets.NOVU_INTERNAL_SECRET_KEY }}

          # The publicly available endpoint hosting the bridge application
          # where notification entities (eg. workflows, topics) are defined.
          # Required.
          bridge-url: https://api.novu-staging.co/v1/bridge/novu

  newrelic:
    runs-on: ubuntu-latest
    name: New Relic Deploy
    needs: deploy_dev_api
    environment: Development
    steps:
      # This step builds a var with the release tag value to use later
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      # This step creates a new Change Tracking Marker
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          region: EU
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: 'MzgxMjQwOHxBUE18QVBQTElDQVRJT058NDk3NjQzODIy'
          version: '${{ env.RELEASE_VERSION }}'
          user: '${{ github.actor }}'
