name: Deploy DEV WEBHOOK

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'apps/webhook/**'
      - 'libs/dal/**'
      - 'packages/shared/**'

env:
  TF_WORKSPACE: novu-dev
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_webhook:
    uses: ./.github/workflows/reusable-webhook-e2e.yml
    secrets: inherit

  publish_docker_image_webhook:
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    needs: test_webhook
    uses: ./.github/workflows/reusable-docker.yml
    with:
      environment: Development
      package_name: novu-dev/webhook
      project_path: apps/webhook
      test_port: 1341
      health_check: true
      local_tag: novu-webhook
      env_tag: dev
      aws-region: eu-west-2
    secrets: inherit

  deploy_dev_webhook:
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    needs: publish_docker_image_webhook
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Development
      service_name: webhook
      terraform_workspace: novu-dev
      docker_image: ${{ needs.publish_docker_image_webhook.outputs.docker_image_ee }}
