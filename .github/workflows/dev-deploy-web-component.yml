name: Deploy DEV Notification Center Web Component

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  workflow_dispatch:
  push:
    branches:
      - next
      - main
    paths:
      - 'packages/notification-center/**'

jobs:
  build:
    uses: ./.github/workflows/reusable-notification-center.yml

  deploy_web_component:
    needs: build
    runs-on: ubuntu-latest
    timeout-minutes: 80
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    environment: Development

    steps:
      - uses: actions/checkout@v4

      - name: Download the artifact
        uses: actions/download-artifact@v4
        with:
          name: notification-center-web-component
          path: packages/notification-center/dist/umd

      - name: Deploy Notification Center Web Component to DEV
        uses: scopsy/actions-netlify@develop
        with:
          publish-dir: packages/notification-center/dist/umd
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: dev
          production-deploy: true
          alias: dev
          github-deployment-environment: development
          github-deployment-description: Notification Center Web Component Deployment
          netlify-config-path: packages/notification-center/netlify.toml
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: 69f27d21-6946-455f-8715-eae2c7931fe0
        timeout-minutes: 1
