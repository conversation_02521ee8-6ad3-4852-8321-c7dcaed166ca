name: Deploy PROD API

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

jobs:
  build_prod_image:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80
    environment: Production
    outputs:
      docker_image: ${{ steps.build-image.outputs.IMAGE }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - name: build api
        run: pnpm build:api --skip-nx-cache

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Prepare
        shell: bash
        run: |
          service="novu/api-ee"
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Set Bull MQ Env variable for EE
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID}}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu/api
          IMAGE_TAG: ${{ github.sha }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          cd apps/api && pnpm --silent --workspace-root pnpm-context -- apps/api/Dockerfile | BULL_MQ_PRO_NPM_TOKEN=${BULL_MQ_PRO_NPM_TOKEN} docker buildx build --secret id=BULL_MQ_PRO_NPM_TOKEN --build-arg PACKAGE_PATH=apps/api - -t novu-api --load $DOCKER_BUILD_ARGUMENTS
          docker tag novu-api $REGISTRY/$REPOSITORY:latest
          docker tag novu-api $REGISTRY/$REPOSITORY:prod
          docker tag novu-api $REGISTRY/$REPOSITORY:$IMAGE_TAG

          docker run --network=host --name api -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:1337/v1/health-check | grep 'ok'

          docker push $REGISTRY/$REPOSITORY:prod
          docker push $REGISTRY/$REPOSITORY:latest
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy_prod_api_eu:
    needs: build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: api
      terraform_workspace: novu-prod-eu
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}

  deploy_prod_api_us:
    needs:
      - build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: api
      terraform_workspace: novu-prod
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}
      deploy_sentry_release: true
      sentry_project: api

  sync_novu_state:
    needs: deploy_prod_api_us
    runs-on: ubuntu-latest
    environment: Production
    steps:
      - name: Sync State to Novu
        uses: novuhq/actions-novu-sync@v2
        with:
          # The secret key used to authenticate with Novu Cloud
          # To get the secret key, go to https://web.novu.co/api-keys.
          # Required.
          secret-key: ${{ secrets.NOVU_INTERNAL_SECRET_KEY }}

          # The publicly available endpoint hosting the bridge application
          # where notification entities (eg. workflows, topics) are defined.
          # Required.
          bridge-url: https://api.novu.co/v1/bridge/novu

  deploy_docs:
    needs: deploy_prod_api_us
    runs-on: ubuntu-latest
    environment: Production
    steps:
      - name: Deploy API Documentation
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ secrets.API_DOCS_BUILD_WEBHOOK }}
          method: 'POST'

  newrelic:
    runs-on: ubuntu-latest
    name: New Relic Deploy
    needs: deploy_prod_api_us
    environment: Production
    steps:
      # This step builds a var with the release tag value to use later
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      # This step creates a new Change Tracking Marker
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          region: EU
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: 'MzgxMjQwOHxBUE18QVBQTElDQVRJT058NDk3NzA2ODk2'
          version: '${{ env.RELEASE_VERSION }}'
          user: '${{ github.actor }}'
