name: Deploy PROD WS

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  # This workflow contains a single job called "build"
  build_prod_image:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80
    environment: Production
    outputs:
      docker_image: ${{ steps.build-image.outputs.IMAGE }}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}
      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - name: Set Bull MQ Env variable for EE
        shell: bash
        run: |
          echo "BULL_MQ_PRO_NPM_TOKEN=${{ secrets.BULL_MQ_PRO_NPM_TOKEN }}" >> $GITHUB_ENV

      - name: build ws
        run: pnpm build:ws

      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: 'image=moby/buildkit:v0.13.1'

      - name: Prepare
        shell: bash
        run: |
          service="novu/ws-ee"
          echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID}}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: novu/ws
          IMAGE_TAG: ${{ github.sha }}
          DOCKER_BUILD_ARGUMENTS: >
            --platform=linux/amd64
            --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
        run: |
          BULL_MQ_PRO_NPM_TOKEN=${BULL_MQ_PRO_NPM_TOKEN} docker buildx build --secret id=BULL_MQ_PRO_NPM_TOKEN -t $REGISTRY/$REPOSITORY:$IMAGE_TAG --load -f apps/ws/Dockerfile . $DOCKER_BUILD_ARGUMENTS
          docker run --network=host --name api -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:1340/v1/health-check | grep 'ok'
          docker tag $REGISTRY/$REPOSITORY:$IMAGE_TAG $REGISTRY/$REPOSITORY:prod
          docker tag $REGISTRY/$REPOSITORY:$IMAGE_TAG $REGISTRY/$REPOSITORY:latest
          docker push $REGISTRY/$REPOSITORY:prod
          docker push $REGISTRY/$REPOSITORY:latest
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy_prod_ws_eu:
    needs:
      - build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: ws
      terraform_workspace: novu-prod-eu
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}
      deploy_sentry_release: true
      sentry_project: ws

  deploy_prod_ws_us:
    needs:
      - build_prod_image
    uses: ./.github/workflows/reusable-app-service-deploy.yml
    secrets: inherit
    with:
      environment: Production
      service_name: ws
      terraform_workspace: novu-prod
      docker_image: ${{ needs.build_prod_image.outputs.docker_image }}
      deploy_sentry_release: true
      sentry_project: ws
