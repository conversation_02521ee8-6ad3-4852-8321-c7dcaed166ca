name: 'Pull Request Labeler'

on:
  - pull_request_target

jobs:
  on_pr:
    permissions:
      contents: read
      pull-requests: write
      statuses: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: PR Labels
        uses: actions/labeler@v4
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: PR Metrics
        uses: microsoft/PR-Metrics@v1.5.7
        env:
          PR_METRICS_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          base-size: 200
          growth-rate: 2.0
          test-factor: 0.5
        continue-on-error: true
