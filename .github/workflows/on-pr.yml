name: Check pull request
concurrency:
  group: '${{ github.workflow }}-${{ github.ref }}'
  cancel-in-progress: true

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  pull_request:
  workflow_dispatch:

jobs:
  dependency-review:
    name: Dependency review
    runs-on: ubuntu-latest
    environment: Linting
    steps:
      - name: 'Checkout Repository'
        uses: actions/checkout@v4
      - name: 'Dependency Review'
        uses: actions/dependency-review-action@v4

  spellcheck:
    name: Spell check
    runs-on: ubuntu-latest
    environment: Linting
    steps:
      - uses: actions/checkout@v4
      - name: Run Spell Check
        uses: streetsidesoftware/cspell-action@v6
        with:
          root: 'apps/web'
          files: '**/*'
          incremental_files_only: true

  find-flags:
    runs-on: ubuntu-latest
    name: Find LaunchDarkly feature flags in diff
    environment: Linting
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Find flags
        uses: launchdarkly/find-code-references-in-pull-request@v2
        id: find-flags
        with:
          project-key: default
          environment-key: production
          access-token: ${{ secrets.LD_ACCESS_TOKEN }}
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  get-affected:
    name: Get Affected Packages
    runs-on: ubuntu-latest
    outputs:
      test-unit: ${{ steps.get-projects-arrays.outputs.test-unit }}
      test-e2e: ${{ steps.get-projects-arrays.outputs.test-e2e }}
      test-e2e-ee: ${{ steps.get-projects-arrays.outputs.test-e2e-ee }}
      test-cypress: ${{ steps.get-projects-arrays.outputs.test-cypress }}
      test-providers: ${{ steps.get-projects-arrays.outputs.test-providers }}
      test-packages: ${{ steps.get-projects-arrays.outputs.test-packages }}
      test-libs: ${{ steps.get-projects-arrays.outputs.test-libs }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      # Get current branch name
      - name: Get branch name
        id: branch-name
        uses: tj-actions/branch-names@v7.0.7
      # Get base branch name to compare with. Base branch on a PR, "main" branch on pushing.
      - name: Get base branch name
        id: get-base-branch-name
        run: |
          if [[ "${{github.event.pull_request.base.ref}}" != "" ]]; then
            echo "branch=${{github.event.pull_request.base.ref}}" >> $GITHUB_OUTPUT
          else
            echo "branch=main" >> $GITHUB_OUTPUT
          fi
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup-project
        with:
          slim: 'true'

      # Configure Nx to be able to detect changes between branches when we are in a PR
      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v2
        with:
          main-branch-name: ${{steps.get-base-branch-name.outputs.branch}}

      - name: Get affected
        id: get-projects-arrays
        # When not in a PR and the current branch is main, pass --all flag. Otherwise pass the base branch
        run: |
          if [[ "${{github.event.pull_request.base.ref}}" == "" && "${{steps.branch-name.outputs.current_branch}}" == "main" ]]; then
            echo "Running ALL"
            echo "test-unit=$(pnpm run get-affected test:unit --all | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-e2e=$(pnpm run get-affected test:e2e --all | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-e2e-ee=$(pnpm run get-affected test:e2e:ee --all | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-cypress=$(pnpm run get-affected cypress:run --all | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-providers=$(pnpm run get-affected test --all providers | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-packages=$(pnpm run get-affected test --all packages | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-libs=$(pnpm run get-affected test --all libs | tail -n +5)" >> $GITHUB_OUTPUT
          else
            echo "Running PR origin/${{steps.get-base-branch-name.outputs.branch}}"
            echo "test-unit=$(pnpm run get-affected test origin/${{steps.get-base-branch-name.outputs.branch}} | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-e2e=$(pnpm run get-affected test:e2e origin/${{steps.get-base-branch-name.outputs.branch}} | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-e2e-ee=$(pnpm run get-affected test:e2e:ee origin/${{steps.get-base-branch-name.outputs.branch}} | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-cypress=$(pnpm run get-affected cypress:run origin/${{steps.get-base-branch-name.outputs.branch}} | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-providers=$(pnpm run get-affected test origin/${{steps.get-base-branch-name.outputs.branch}} providers | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-packages=$(pnpm run get-affected test origin/${{steps.get-base-branch-name.outputs.branch}} packages | tail -n +5)" >> $GITHUB_OUTPUT
            echo "test-libs=$(pnpm run get-affected test origin/${{steps.get-base-branch-name.outputs.branch}} libs | tail -n +5)" >> $GITHUB_OUTPUT
          fi

  test_unit_providers:
    name: Unit test @novu/providers
    runs-on: ubuntu-latest
    needs: [get-affected]
    if: ${{ fromJson(needs.get-affected.outputs.test-providers)[0] }}
    timeout-minutes: 80
    steps:
      - run: echo '${{ needs.get-affected.outputs.test-providers }}'
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-project
        with:
          slim: 'true'
      - uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: lint,build,test
          parallel: 5
          projects: ${{join(fromJson(needs.get-affected.outputs.test-providers), ',')}}

  test_unit_packages:
    name: Unit test @novu public NPM packages (except providers)
    runs-on: ubuntu-latest
    needs: [get-affected]
    if: ${{ fromJson(needs.get-affected.outputs.test-packages)[0] }}
    timeout-minutes: 80
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - name: Affected packages
        run: echo '${{ needs.get-affected.outputs.test-packages }}'

      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup-project
        with:
          slim: 'true'

      - name: Run Lint, Build, Test
        uses: mansagroup/nrwl-nx-action@v3
        env:
          LOGGING_LEVEL: 'info'
        with:
          targets: lint,build,test
          projects: ${{join(fromJson(needs.get-affected.outputs.test-packages), ',')}}

  test_unit_libs:
    name: Unit test @novu internal packages
    runs-on: ubuntu-latest
    needs: [get-affected]
    if: ${{ fromJson(needs.get-affected.outputs.test-libs)[0] }}
    timeout-minutes: 80
    steps:
      - name: Affected libs
        run: echo '${{ needs.get-affected.outputs.test-libs }}'
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup-project

      - name: Run Lint, Build, Test
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: lint,build,test
          projects: ${{join(fromJson(needs.get-affected.outputs.test-libs), ',')}}

  test_unit_services:
    name: Unit test backend services
    runs-on: ubuntu-latest
    needs: [get-affected]
    if: ${{ fromJson(needs.get-affected.outputs.test-unit)[0] }}
    timeout-minutes: 80
    strategy:
      # One job for each different project and node version
      matrix:
        projectName: ${{ fromJson(needs.get-affected.outputs.test-unit) }}
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - run: echo ${{ matrix.projectName }}
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-project
        with:
          # Don't run redis and etc... for other unit tests
          slim: ${{ !contains(matrix.projectName, '@novu/api-service') && !contains(matrix.projectName, '@novu/worker') && !contains(matrix.projectName, '@novu/ws') && !contains(matrix.projectName, '@novu/inbound-mail')}}
      - uses: ./.github/actions/setup-redis-cluster
      - uses: mansagroup/nrwl-nx-action@v3
        name: Lint and build and test
        with:
          targets: lint,build,test
          projects: ${{matrix.projectName}}

  validate_openapi:
    name: Validate OpenAPI
    runs-on: ubuntu-latest
    needs: [get-affected]
    if: ${{ fromJson(needs.get-affected.outputs.test-unit)[0] }}
    timeout-minutes: 10
    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-project
      - uses: ./.github/actions/setup-redis-cluster
      - uses: ./.github/actions/run-api
        with:
          launch_darkly_sdk_key: ${{ secrets.LAUNCH_DARKLY_SDK_KEY }}

      - uses: ./.github/actions/validate-openapi

  test_e2e_api:
    name: E2E test API
    needs: [get-affected]
    strategy:
      # The order is important for ee to be first, otherwise outputs not work correctly
      matrix:
        name: ['novu/api-ee', 'novu/api']
    uses: ./.github/workflows/reusable-api-e2e.yml
    with:
      ee: ${{ contains (matrix.name,'-ee') }}
      test-e2e-affected: ${{ contains(fromJson(needs.get-affected.outputs.test-e2e), '@novu/api-service') || contains(fromJson(needs.get-affected.outputs.test-e2e), '@novu/worker') }}
      test-e2e-ee-affected: ${{ contains(fromJson(needs.get-affected.outputs.test-e2e-ee), '@novu/api-service') || contains(fromJson(needs.get-affected.outputs.test-e2e-ee), '@novu/worker') }}
      job-name: ${{ matrix.name }}
      test-unit: false
    secrets: inherit

  test_e2e_web:
    name: E2E test Web app
    needs: [get-affected]
    if: ${{ contains(fromJson(needs.get-affected.outputs.test-e2e), '@novu/web') }}
    uses: ./.github/workflows/reusable-web-e2e.yml
    secrets: inherit
    with:
      ee: true

  test_e2e_dashboard:
    name: E2E test Dashboard app
    needs: [get-affected]
    if: ${{ contains(fromJson(needs.get-affected.outputs.test-e2e), '@novu/dashboard') }}
    uses: ./.github/workflows/reusable-dashboard-e2e.yml
    secrets: inherit
    with:
      ee: true

  test_e2e_widget:
    name: E2E test Widget
    needs: [get-affected]
    uses: ./.github/workflows/reusable-widget-e2e.yml
    with:
      ee: true
    if: ${{ contains(fromJson(needs.get-affected.outputs.test-cypress), '@novu/widget') || contains(fromJson(needs.get-affected.outputs.test-unit), '@novu/notification-center') || contains(fromJson(needs.get-affected.outputs.test-unit), '@novu/ws') }}
    secrets: inherit
