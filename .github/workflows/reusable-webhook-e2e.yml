name: E2E WEBHOOK Tests

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_call:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  e2e_webhook:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    timeout-minutes: 80

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup-project

      - uses: ./.github/actions/start-localstack

        # Runs a single command using the runners shell
      - name: Build Webhook
        run: CI='' pnpm build:webhook

      # Runs a set of commands using the runners shell
      - name: Run a test
        run: |
          cd apps/webhook && pnpm test:e2e
