name: Test and build @novu/notification-center

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  workflow_call:

jobs:
  build_test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup-project

      - uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build,test
          projects: '@novu/notification-center'
          nxCloud: true

      - uses: actions/upload-artifact@v4
        with:
          name: notification-center-web-component
          path: packages/notification-center/dist/umd
