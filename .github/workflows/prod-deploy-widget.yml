name: Deploy PROD Widget

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test_widget:
    uses: ./.github/workflows/reusable-widget-e2e.yml
    with:
      ee: true
    secrets: inherit

  deploy_widget_eu:
    needs: test_widget
    uses: ./.github/workflows/reusable-widget-deploy.yml
    with:
      environment: Production
      react_app_api_url: https://eu.api.novu.co
      react_app_ws_url: https://eu.ws.novu.co
      react_app_webhook_url: https://eu.webhook.novu.co
      react_app_sentry_dsn: https://<EMAIL>/625116
      react_app_environment: production
      netlify_deploy_message: Prod deployment
      netlify_alias: prod
      netlify_gh_env: Production
      netlify_site_id: 20a64bdd-1934-4284-875f-862410c69a3b
    secrets: inherit

  deploy_widget_us:
    needs:
      - test_widget
      - deploy_widget_eu
    uses: ./.github/workflows/reusable-widget-deploy.yml
    with:
      environment: Production
      react_app_api_url: https://api.novu.co
      react_app_ws_url: https://ws.novu.co
      react_app_webhook_url: https://webhook.novu.co
      react_app_sentry_dsn: https://<EMAIL>/625116
      react_app_environment: production
      netlify_deploy_message: Prod deployment
      netlify_alias: prod
      netlify_gh_env: Production
      netlify_site_id: 6f927fd4-dcb0-4cf3-8c0b-8c5539d0d034
    secrets: inherit

  deploy_docker:
    needs:
      - deploy_widget_us
      - deploy_widget_eu
    uses: ./.github/workflows/reusable-docker.yml
    with:
      environment: Production
      package_name: novu/widget
      project_path: apps/widget
      local_tag: novu-widget
      env_tag: prod
    secrets: inherit
