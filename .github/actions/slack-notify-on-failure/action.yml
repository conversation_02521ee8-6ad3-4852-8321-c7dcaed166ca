name: 'Notify Slack on workflow failure'

inputs:
  slackWebhookURL:
    required: true
    type: string

runs:
  using: 'composite'
  steps:
    - name: Notify Slack Action
      if: ${{ github.ref_name == 'next' || github.ref_name == 'main' || github.ref_name == 'prod' }}
      uses: ravsamhq/notify-slack-action@v2
      with:
        footer: "Run: {run_url}\nCommit: {commit_url}"
        message_format: '{emoji} *{workflow}* {status_message} in <{repo_url}|{repo}>'
        notification_title: '{workflow} is now failing!'
        notify_when: 'failure'
        status: ${{ job.status }}
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.slackWebhookURL }}
