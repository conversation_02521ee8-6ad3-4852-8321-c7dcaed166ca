name: Run Backend

description: Starts and waits for the API and Worker instance

inputs:
  launch_darkly_sdk_key:
    description: 'The Launch Darkly SDK key to use'
    required: false
    default: ''
  cypress_github_oauth_client_id:
    description: 'Cypress GitHub client ID'
    required: true
  cypress_github_oauth_client_secret:
    description: 'Cypress GitHub client secret'
    required: true
  ci_ee_test:
    description: 'Whether the app should import ee packages for testing'
    required: false
    default: 'false'

runs:
  using: composite

  steps:
    - name: Start API in TEST
      shell: bash
      env:
        GITHUB_OAUTH_CLIENT_ID: ${{ inputs.cypress_github_oauth_client_id }}
        GITHUB_OAUTH_CLIENT_SECRET: ${{ inputs.cypress_github_oauth_client_secret }}
        NODE_ENV: 'test'
        PORT: '1336'
        GITHUB_OAUTH_REDIRECT: 'http://127.0.0.1:1336/v1/auth/github/callback'
        LAUNCH_DARKLY_SDK_KEY: ${{ inputs.launch_darkly_sdk_key }}
        CI_EE_TEST: ${{ inputs.ci_ee_test }}
      run: cd apps/api && pnpm start:prod &

    - name: Start Worker
      shell: bash
      env:
        NODE_ENV: 'test'
        PORT: '1342'
        LAUNCH_DARKLY_SDK_KEY: ${{ inputs.launch_darkly_sdk_key }}
        CI_EE_TEST: ${{ inputs.ci_ee_test }}
      run: cd apps/worker && pnpm start:prod &

    - name: Wait on API and Worker
      shell: bash
      run: wait-on --timeout=180000 http://127.0.0.1:1336/v1/health-check http://127.0.0.1:1342/v1/health-check
