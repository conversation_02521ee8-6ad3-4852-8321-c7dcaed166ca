name: Start LocalStack

description: Sets up the LocalStack

runs:
  using: composite
  steps:
    - name: Start LocalStack
      shell: bash
      env:
        AWS_DEFAULT_REGION: us-east-1
        DEFAULT_REGION: us-east-1
        AWS_ACCOUNT_ID: '************'
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        AWS_EC2_METADATA_DISABLED: true
      working-directory: docker/local
      run: |
        docker compose -f docker-compose.e2e.yml up -d
        sleep 10
        max_retry=30
        counter=0
        until $command
        do
          sleep 1
          [[ counter -eq $max_retry ]] && echo "Failed!" && exit 1
          aws --endpoint-url=http://127.0.0.1:4566 s3 ls
          echo "Trying again. Try #$counter"
          ((counter++))
        done
        docker compose -f docker-compose.e2e.yml logs --tail="all"
        aws --endpoint-url=http://127.0.0.1:4566 --cli-connect-timeout 600 s3 mb s3://novu-test
