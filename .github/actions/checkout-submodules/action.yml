name: Checkout Submodules

description: Checkout private enterprise submodule

inputs:
  enabled:
    description: 'Run the action'
    required: false
    default: 'true'
  submodule_token:
    description: 'Submodule token to use for checkout'
    required: true
  submodule_branch:
    description: 'Submodule branch to checkout to'
    required: true

runs:
  using: composite

  steps:
    - name: Checkout submodule
      if: ${{ inputs.enabled == 'true' }}
      uses: actions/checkout@v4
      with:
        token: ${{ inputs.submodule_token }}
        repository: novuhq/packages-enterprise
        path: enterprise/packages
        ref: ${{ inputs.submodule_branch }}
