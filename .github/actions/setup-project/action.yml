name: Setup Novu Monorepo

description: Sets up the whole monorepo and install dependencies

inputs:
  slim:
    description: 'Should only install dependencies and checkout code'
    required: false
    default: 'false'
  submodules:
    description: 'Should link submodules'
    required: false
    default: 'false'
outputs:
  cypress_cache_hit:
    description: 'Did cypress use binary cache'
    value: ${{ inputs.cypress_version != '' && steps.cache-cypress-binary-version.outputs.cache-hit || steps.cache-cypress-binary.outputs.cache-hit}}

runs:
  using: composite
  steps:
    # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
    - name: ⚙️ Setup kernel for react native, increase watchers
      shell: bash
      run: echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p

    - name: Install pnpm
      uses: pnpm/action-setup@v3

    - uses: actions/setup-node@v4
      name: ⚙️ Setup Node Version
      with:
        node-version: '20.8.1'
        cache: 'pnpm'

    - name: 💵 Start Redis
      if: ${{ inputs.slim == 'false' }}
      uses: supercharge/redis-github-action@1.5.0

    - name: 📚 Start MongoDB
      if: ${{ inputs.slim == 'false' }}
      uses: supercharge/mongodb-github-action@1.11.0
      with:
        mongodb-version: 5.0.29

    - name: 🛟 Install dependencies
      shell: bash
      run: pnpm install --frozen-lockfile

    - name: Link submodules
      shell: bash
      if: ${{ inputs.submodules == 'true' }}
      run: pnpm symlink:submodules

    - name: Install wait-on plugin
      shell: bash
      run: pnpm i -g wait-on
