name: Build Worker Docker Image

description: 'Builds a docker image for the Worker'

inputs:
  tag:
    description: 'The tag to use for the image'
    required: false
  push:
    description: 'Push the image to the registry'
    required: false
    default: 'false'
  aws-access-key-id:
    description: 'Access Key for AWS'
    required: true
  aws-secret-access-key:
    description: 'Secret Access Key for AWS'
    required: true
  fork:
    description: 'Whether this is being triggered from a forked repo'
    required: false
    default: 'false'
  docker_name:
    description: 'Name for docker image'
    required: true
  bullmq_secret:
    description: 'Bullmq secret api token'
    required: true

outputs:
  image:
    description: 'The image that was built'
    value: ${{ steps.build-image.outputs.IMAGE }}

runs:
  using: composite
  steps:
    - name: Set Up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: 'image=moby/buildkit:v0.13.1'

    - name: Prepare
      shell: bash
      run: |
        service=${{ matrix.name }}
        echo "SERVICE_NAME=$(basename "${service//-/-}")" >> $GITHUB_ENV

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: eu-west-2

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Set Bull MQ Env variable for EE
      shell: bash
      run: |
        echo "BULL_MQ_PRO_NPM_TOKEN=${{ inputs.bullmq_secret }}" >> $GITHUB_ENV
      if: contains(inputs.docker_name , 'ee')

    - name: Build with Buildx, tag, and test
      shell: bash
      env:
        REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        REPOSITORY: novu-dev/worker
        IMAGE_TAG: ${{ github.sha }}
        DOCKER_BUILD_ARGUMENTS: >
          --platform=linux/amd64 --provenance=false
          --output=type=image,name=$REGISTRY/$REPOSITORY,push-by-digest=true,name-canonical=true
      run: |
        set -x
        cd apps/worker && pnpm run docker:build

    - name: Tag and test
      id: build-image
      shell: bash
      env:
        REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        REPOSITORY: novu-dev/worker
        IMAGE_TAG: ${{ github.sha }}
      run: |
        echo "Built image"
        docker tag novu-worker $REGISTRY/$REPOSITORY:$IMAGE_TAG

        docker run --network=host --name worker -dit --env NODE_ENV=test $REGISTRY/$REPOSITORY:$IMAGE_TAG
        docker run --network=host appropriate/curl --retry 10 --retry-delay 5 --retry-connrefused http://127.0.0.1:1342/v1/health-check | grep 'ok'

        echo "IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Optionally tag docker image
      if: ${{ inputs.tag }}
      shell: bash
      env:
        REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        REPOSITORY: novu-dev/worker
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker tag $REGISTRY/$REPOSITORY:$IMAGE_TAG $REGISTRY/$REPOSITORY:${{ inputs.tag }}

    - name: Push PR tag image
      if: ${{ inputs.push == 'true' }}
      shell: bash
      env:
        REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        REPOSITORY: novu-dev/worker
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

    - name: Push custom tag image
      if: ${{ inputs.push == 'true' && inputs.tag }}
      shell: bash
      env:
        REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        REPOSITORY: novu-dev/worker
      run: |
        docker push $REGISTRY/$REPOSITORY:${{ inputs.tag }}
