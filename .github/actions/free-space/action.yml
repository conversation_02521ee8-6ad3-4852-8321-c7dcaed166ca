name: Extend Disk Space
description: This action removes some preinstalled tools in favor of opening space for our docker runs with QEMU
runs:
  using: composite
  steps:
    - name: Run script
      run: |
        set -eux

        df -h
        echo "::group::apt clean"
        sudo apt clean
        echo "::endgroup::"

        echo "::group::/usr/local/*"
        du -hsc /usr/local/*
        echo "::endgroup::"
        # ~1GB
        sudo rm -rf \
          /usr/local/aws-sam-cil \
          /usr/local/julia* || :
        echo "::group::/usr/local/bin/*"
          du -hsc /usr/local/bin/*
        echo "::endgroup::"
        # ~1GB (From 1.2GB to 214MB)
        sudo rm -rf \
          /usr/local/bin/aliyun \
          /usr/local/bin/azcopy \
          /usr/local/bin/bicep \
          /usr/local/bin/cmake-gui \
          /usr/local/bin/cpack \
          /usr/local/bin/hub \
          /usr/local/bin/kubectl \
          /usr/local/bin/minikube \
          /usr/local/bin/packer \
          /usr/local/bin/pulumi* \
          /usr/local/bin/sam \
          /usr/local/bin/stack || :
        # 142M
        sudo rm -rf /usr/local/bin/oc || : \
        echo "::group::/usr/local/share/*"
        du -hsc /usr/local/share/*
        echo "::endgroup::"
        # 506MB
        sudo rm -rf /usr/local/share/chromium || :
        # 1.3GB
        sudo rm -rf /usr/local/share/powershell || :
        echo "::group::/usr/local/lib/*"
        du -hsc /usr/local/lib/*
        echo "::endgroup::"
        # 15GB
        sudo rm -rf /usr/local/lib/android || :
        # 341MB
        sudo rm -rf /usr/local/lib/heroku || :
        # 679MB
        sudo rm -rf /opt/az || :
        echo "::group::/opt/microsoft/*"
        du -hsc /opt/microsoft/*
        echo "::endgroup::"
        # 197MB
        sudo rm -rf /opt/microsoft/powershell || :
        echo "::group::/opt/hostedtoolcache/*"
        du -hsc /opt/hostedtoolcache/*
        echo "::endgroup::"
        # 5.3GB
        sudo rm -rf /opt/hostedtoolcache/CodeQL || :
        # 1.4GB
        sudo rm -rf /opt/hostedtoolcache/go || :

        df -h
      shell: bash
