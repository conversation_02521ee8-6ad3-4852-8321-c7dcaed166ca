name: Run API

description: Starts and waits for an API running instance

inputs:
  launch_darkly_sdk_key:
    description: 'The Launch Darkly SDK key to use'
    required: false
    default: ''

runs:
  using: composite

  steps:
    - uses: mansagroup/nrwl-nx-action@v3
      with:
        targets: build
        projects: '@novu/api-service'

    - name: Start API
      shell: bash
      env:
        LAUNCH_DARKLY_SDK_KEY: ${{ inputs.launch_darkly_sdk_key }}
        NODE_ENV: 'test'
        PORT: '1336'
      run: cd apps/api && pnpm start:prod &

    - name: Wait on API
      shell: bash
      run: wait-on --timeout=180000 http://127.0.0.1:1336/v1/health-check
