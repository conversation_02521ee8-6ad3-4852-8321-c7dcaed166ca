name: Run Worker

description: Sets up a Redis Cluster instance needed to run the tests

inputs:
  launch_darkly_sdk_key:
    description: 'The Launch Darkly SDK key to use'
    required: false
    default: ''
  ee:
    description: 'Whether to run the EE version of the tests'
    required: false
    default: false
    type: boolean

runs:
  using: composite

  steps:
    - name: Build worker
      shell: bash
      run: CI='' pnpm build:worker --skip-nx-cache

    - name: Start worker
      shell: bash
      env:
        LAUNCH_DARKLY_SDK_KEY: ${{ inputs.launch_darkly_sdk_key }}
        NOVU_ENTERPRISE: ${{ inputs.ee }}
      run: cd apps/worker && pnpm start:test &

    - name: Wait on worker
      shell: bash
      run: wait-on --timeout=180000 http://127.0.0.1:1342/v1/health-check
